// 配置管理模块
class ConfigManager {
    constructor() {
        this.currentEditingProduct = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadRooms();
        this.loadProducts();
    }

    bindEvents() {
        // 添加直播间
        document.getElementById('addRoomBtn').addEventListener('click', () => {
            this.addRoom();
        });

        // 添加产品
        document.getElementById('addProductBtn').addEventListener('click', () => {
            this.showProductForm();
        });

        // 保存配置
        document.getElementById('saveConfigBtn').addEventListener('click', () => {
            this.exportConfig();
        });

        // 加载本地配置
        document.getElementById('loadLocalConfigBtn').addEventListener('click', () => {
            document.getElementById('loadConfigFile').click();
        });

        document.getElementById('loadConfigFile').addEventListener('change', (e) => {
            this.importConfig(e.target.files[0]);
        });

        // 加载线上配置
        document.getElementById('loadOnlineConfigBtn').addEventListener('click', () => {
            this.showLoadOnlineConfigModal();
        });

        // 批量导入
        document.getElementById('batchImportBtn').addEventListener('click', () => {
            this.showBatchImportModal();
        });

        // 批量修改
        document.getElementById('batchEditBtn').addEventListener('click', () => {
            this.showBatchEditModal();
        });

        // 清空配置
        document.getElementById('clearConfigBtn').addEventListener('click', () => {
            this.showClearConfigModal();
        });

        // 产品类型选择
        document.getElementById('productType').addEventListener('change', () => {
            this.updateProductForm();
        });
    }

    // 加载直播间列表
    loadRooms() {
        const rooms = window.storageManager.getRooms();
        const roomsList = document.getElementById('roomsList');
        const selectedRoom = document.getElementById('selectedRoom');
        
        // 更新直播间列表
        roomsList.innerHTML = '';
        selectedRoom.innerHTML = '<option value="">选择直播间</option>';
        
        rooms.forEach(room => {
            // 添加到管理列表
            const roomDiv = document.createElement('div');
            roomDiv.className = 'flex items-center justify-between bg-gray-700 p-3 rounded';
            roomDiv.innerHTML = `
                <span class="font-medium text-lg">${room}</span>
                <button onclick="configManager.removeRoom('${room}')" 
                        class="text-red-400 hover:text-red-300 transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            `;
            roomsList.appendChild(roomDiv);
            
            // 添加到选择下拉框
            const option = document.createElement('option');
            option.value = room;
            option.textContent = room;
            selectedRoom.appendChild(option);
        });
    }

    // 添加直播间
    addRoom() {
        const input = document.getElementById('newRoomName');
        const roomName = input.value.trim();
        
        if (!roomName) {
            this.showMessage('请输入直播间名称', 'error');
            return;
        }
        
        if (window.storageManager.addRoom(roomName)) {
            input.value = '';
            this.loadRooms();
            this.showMessage('直播间添加成功', 'success');
        } else {
            this.showMessage('直播间已存在或添加失败', 'error');
        }
    }

    // 删除直播间
    removeRoom(roomName) {
        if (confirm(`确定要删除直播间"${roomName}"吗？这将同时删除该直播间下的所有产品配置。`)) {
            if (window.storageManager.removeRoom(roomName)) {
                this.loadRooms();
                this.loadProducts();
                this.showMessage('直播间删除成功', 'success');
            } else {
                this.showMessage('删除失败', 'error');
            }
        }
    }

    // 显示产品配置表单
    showProductForm() {
        const selectedRoom = document.getElementById('selectedRoom').value;
        const productType = document.getElementById('productType').value;
        
        if (!selectedRoom) {
            this.showMessage('请先选择直播间', 'error');
            return;
        }
        
        if (!productType) {
            this.showMessage('请选择产品类型', 'error');
            return;
        }
        
        this.currentEditingProduct = null;
        this.updateProductForm();
        document.getElementById('productForm').classList.remove('hidden');
    }

    // 更新产品配置表单
    updateProductForm() {
        const productType = document.getElementById('productType').value;
        const productForm = document.getElementById('productForm');
        
        if (!productType) {
            productForm.classList.add('hidden');
            return;
        }
        
        let formHTML = '';
        
        if (productType === 'redpack') {
            formHTML = this.getRedpackForm();
        } else if (productType === 'lucky') {
            formHTML = this.getLuckyForm();
        }
        
        productForm.innerHTML = `
            <h3 class="text-lg font-semibold mb-4 text-yellow-400">
                ${productType === 'redpack' ? '红包配置' : '幸运助手配置'}
            </h3>
            ${formHTML}
            <div class="flex space-x-4 mt-6">
                <button onclick="configManager.saveProduct()" 
                        class="bg-green-600 hover:bg-green-700 px-6 py-2 rounded transition-colors">
                    保存配置
                </button>
                <button onclick="configManager.cancelProductForm()" 
                        class="bg-gray-600 hover:bg-gray-700 px-6 py-2 rounded transition-colors">
                    取消
                </button>
            </div>
        `;
    }

    // 获取红包配置表单
    getRedpackForm() {
        return `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-2">红包发送时间</label>
                    <input type="datetime-local" id="redpack_sendTime" 
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">开奖时间</label>
                    <select id="redpack_drawTime" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        <option value="3">3分钟</option>
                        <option value="10">10分钟</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">当前轮次</label>
                    <input type="number" id="redpack_currentRound" min="1" 
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">总轮次</label>
                    <input type="number" id="redpack_totalRounds" min="1" 
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">参与方式</label>
                    <select id="redpack_participationType" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        <option value="fanclub">粉丝团红包</option>
                        <option value="share">分享红包</option>
                        <option value="condition">条件型普通快币红包</option>
                        <option value="password">口令红包</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">快币数</label>
                    <input type="number" id="redpack_coins" min="0" max="99999999" 
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div class="md:col-span-2" id="redpack_passwordField" style="display: none;">
                    <label class="block text-sm font-medium mb-2">口令内容</label>
                    <input type="text" id="redpack_password" 
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
            </div>
            <script>
                document.getElementById('redpack_participationType').addEventListener('change', function() {
                    const passwordField = document.getElementById('redpack_passwordField');
                    if (this.value === 'password') {
                        passwordField.style.display = 'block';
                    } else {
                        passwordField.style.display = 'none';
                    }
                });
            </script>
        `;
    }

    // 获取幸运助手配置表单
    getLuckyForm() {
        return `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-2">参与条件</label>
                    <select id="lucky_condition" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        <option value="comment">评论</option>
                        <option value="like">点赞</option>
                        <option value="fanclub">粉丝团</option>
                        <option value="share">分享</option>
                        <option value="follow">关注主播</option>
                        <option value="watchtime">观看时长</option>
                        <option value="superfan">超粉团</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">中奖人数</label>
                    <input type="number" id="lucky_winners" min="1"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">当前轮次</label>
                    <input type="number" id="lucky_currentRound" min="1"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">总轮次</label>
                    <input type="number" id="lucky_totalRounds" min="1"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">开奖倒计时（分钟）</label>
                    <input type="number" id="lucky_countdown" min="1" max="60"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">定时发送时间</label>
                    <input type="datetime-local" id="lucky_scheduledTime"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium mb-2">奖品说明</label>
                    <textarea id="lucky_prizeDescription" rows="3"
                              class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"></textarea>
                </div>
                <div id="lucky_watchtimeField" style="display: none;">
                    <label class="block text-sm font-medium mb-2">观看时长要求（分钟）</label>
                    <input type="number" id="lucky_watchtime" min="1"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
            </div>
            <script>
                document.getElementById('lucky_condition').addEventListener('change', function() {
                    const watchtimeField = document.getElementById('lucky_watchtimeField');
                    if (this.value === 'watchtime') {
                        watchtimeField.style.display = 'block';
                    } else {
                        watchtimeField.style.display = 'none';
                    }
                });
            </script>
        `;
    }

    // 保存产品配置
    saveProduct() {
        const selectedRoom = document.getElementById('selectedRoom').value;
        const productType = document.getElementById('productType').value;
        
        let productData = {
            room: selectedRoom,
            type: productType
        };
        
        if (productType === 'redpack') {
            productData = { ...productData, ...this.getRedpackData() };
        } else if (productType === 'lucky') {
            productData = { ...productData, ...this.getLuckyData() };
        }
        
        // 验证数据
        if (!this.validateProductData(productData)) {
            return;
        }
        
        if (this.currentEditingProduct) {
            // 更新现有产品
            if (window.storageManager.updateProduct(this.currentEditingProduct, productData)) {
                this.showMessage('产品配置更新成功', 'success');
            } else {
                this.showMessage('更新失败', 'error');
                return;
            }
        } else {
            // 添加新产品
            const productId = window.storageManager.addProduct(productData);
            if (productId) {
                this.showMessage('产品配置添加成功', 'success');
            } else {
                this.showMessage('添加失败', 'error');
                return;
            }
        }

        this.cancelProductForm();
        this.loadProducts();

        // 通知Dashboard更新
        if (window.dashboardManager) {
            window.dashboardManager.updateDashboard();
        }

        // 跳转到配置列表锚点
        this.scrollToProductList();
    }

    // 获取红包数据
    getRedpackData() {
        return {
            sendTime: document.getElementById('redpack_sendTime').value,
            drawTime: parseInt(document.getElementById('redpack_drawTime').value),
            currentRound: parseInt(document.getElementById('redpack_currentRound').value),
            totalRounds: parseInt(document.getElementById('redpack_totalRounds').value),
            participationType: document.getElementById('redpack_participationType').value,
            coins: parseInt(document.getElementById('redpack_coins').value),
            password: document.getElementById('redpack_password')?.value || ''
        };
    }

    // 获取幸运助手数据
    getLuckyData() {
        return {
            condition: document.getElementById('lucky_condition').value,
            winners: parseInt(document.getElementById('lucky_winners').value),
            currentRound: parseInt(document.getElementById('lucky_currentRound').value),
            totalRounds: parseInt(document.getElementById('lucky_totalRounds').value),
            countdown: parseInt(document.getElementById('lucky_countdown').value),
            scheduledTime: document.getElementById('lucky_scheduledTime').value,
            prizeDescription: document.getElementById('lucky_prizeDescription').value,
            watchtime: parseInt(document.getElementById('lucky_watchtime')?.value || 0)
        };
    }

    // 验证产品数据
    validateProductData(data) {
        // 首先进行冲突校验
        if (!this.validateProductConflicts(data)) {
            return false;
        }

        if (data.type === 'redpack') {
            if (!data.sendTime) {
                this.showMessage('请设置红包发送时间', 'error');
                return false;
            }
            if (!data.currentRound || !data.totalRounds) {
                this.showMessage('请设置轮次信息', 'error');
                return false;
            }
            if (data.currentRound > data.totalRounds) {
                this.showMessage('当前轮次不能大于总轮次', 'error');
                return false;
            }
            if (!data.coins || data.coins < 0) {
                this.showMessage('请设置有效的快币数', 'error');
                return false;
            }
        } else if (data.type === 'lucky') {
            if (!data.scheduledTime) {
                this.showMessage('请设置定时发送时间', 'error');
                return false;
            }
            if (!data.winners || data.winners < 1) {
                this.showMessage('请设置有效的中奖人数', 'error');
                return false;
            }
            if (!data.currentRound || !data.totalRounds) {
                this.showMessage('请设置轮次信息', 'error');
                return false;
            }
            if (data.currentRound > data.totalRounds) {
                this.showMessage('当前轮次不能大于总轮次', 'error');
                return false;
            }
            if (!data.countdown || data.countdown < 1 || data.countdown > 60) {
                this.showMessage('开奖倒计时必须在1-60分钟之间', 'error');
                return false;
            }
            if (data.condition === 'watchtime' && data.countdown <= data.watchtime) {
                this.showMessage('观看时长类幸运星，开奖倒计时必须大于观看时长', 'error');
                return false;
            }

            // 检查时间间隔限制
            if (!this.validateLuckyTimeInterval(data.scheduledTime)) {
                this.showMessage('幸运星活动时间间隔必须大于1分钟', 'error');
                return false;
            }
        }
        
        return true;
    }

    // 验证产品配置冲突
    validateProductConflicts(data) {
        const products = window.storageManager.getProducts();
        const productTime = new Date(data.sendTime || data.scheduledTime);

        for (let product of products) {
            // 跳过当前编辑的产品
            if (product.id === this.currentEditingProduct) {
                continue;
            }

            // 检查同一直播间的冲突
            if (product.room === data.room) {
                // 检查相同轮次冲突
                if (product.currentRound === data.currentRound &&
                    product.totalRounds === data.totalRounds) {
                    this.showMessage(`直播间"${data.room}"已存在第${data.currentRound}/${data.totalRounds}轮配置，不可重复设置`, 'error');
                    return false;
                }

                // 检查相同时间冲突
                const existingTime = new Date(product.sendTime || product.scheduledTime);
                if (Math.abs(productTime - existingTime) < 60000) { // 1分钟内视为相同时间
                    const timeStr = productTime.toLocaleString('zh-CN');
                    this.showMessage(`直播间"${data.room}"在时间"${timeStr}"附近已有配置，不可重复设置`, 'error');
                    return false;
                }
            }
        }

        return true;
    }

    // 验证幸运星时间间隔
    validateLuckyTimeInterval(newTime) {
        const products = window.storageManager.getProducts();
        const newDateTime = new Date(newTime);

        for (let product of products) {
            if (product.type === 'lucky' && product.id !== this.currentEditingProduct) {
                const existingTime = new Date(product.scheduledTime);
                const timeDiff = Math.abs(newDateTime - existingTime);
                if (timeDiff < 60000) { // 小于1分钟
                    return false;
                }
            }
        }

        return true;
    }

    // 取消产品表单
    cancelProductForm() {
        document.getElementById('productForm').classList.add('hidden');
        document.getElementById('productType').value = '';
        this.currentEditingProduct = null;
    }

    // 加载产品配置列表
    loadProducts() {
        const products = window.storageManager.getProducts();
        const configList = document.getElementById('configList');
        
        if (products.length === 0) {
            configList.innerHTML = '<div class="text-gray-400 text-center py-8">暂无配置项</div>';
            return;
        }
        
        configList.innerHTML = products.map(product => {
            const timeStr = product.sendTime || product.scheduledTime;
            const time = timeStr ? new Date(timeStr).toLocaleString('zh-CN') : '未设置';
            
            let details = '';
            if (product.type === 'redpack') {
                details = `第${product.currentRound}/${product.totalRounds}轮 | ${product.coins}快币 | ${this.getParticipationTypeText(product.participationType)} | 开奖时间：${product.drawTime}分钟`;
            } else if (product.type === 'lucky') {
                const roundInfo = product.currentRound && product.totalRounds ?
                    `第${product.currentRound}/${product.totalRounds}轮 | ` : '';
                details = `${roundInfo}${product.winners}人中奖 | ${this.getConditionText(product.condition)} | ${product.countdown}分钟倒计时`;
            }

            return `
                <div class="bg-gray-700 p-4 rounded-lg">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="flex items-center space-x-4 mb-2">
                                <span class="text-lg font-semibold text-blue-400">${product.room}</span>
                                <span class="px-2 py-1 rounded text-xs ${product.type === 'redpack' ? 'bg-orange-600' : 'bg-green-600'} text-white">
                                    ${product.type === 'redpack' ? '红包' : '幸运助手'}
                                </span>
                                <span class="text-yellow-400 font-mono">${time}</span>
                            </div>
                            <div class="text-sm text-gray-300">${details}</div>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="configManager.copyProduct('${product.id}')"
                                    class="text-green-400 hover:text-green-300 transition-colors" title="复制配置">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                            <button onclick="configManager.editProduct('${product.id}')"
                                    class="text-blue-400 hover:text-blue-300 transition-colors" title="编辑配置">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </button>
                            <button onclick="configManager.removeProduct('${product.id}')"
                                    class="text-red-400 hover:text-red-300 transition-colors" title="删除配置">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    // 编辑产品
    editProduct(productId) {
        const products = window.storageManager.getProducts();
        const product = products.find(p => p.id === productId);

        if (!product) return;

        this.currentEditingProduct = productId;
        this.fillProductForm(product);

        // 滚动到编辑表单
        this.scrollToProductForm();
    }

    // 复制产品
    copyProduct(productId) {
        const products = window.storageManager.getProducts();
        const product = products.find(p => p.id === productId);

        if (!product) return;

        this.currentEditingProduct = null; // 设为null表示新建
        this.fillProductForm(product, true); // true表示复制模式

        // 滚动到编辑表单
        this.scrollToProductForm();
    }

    // 填充产品表单
    fillProductForm(product, isCopy = false) {
        // 设置表单值
        document.getElementById('selectedRoom').value = product.room;
        document.getElementById('productType').value = product.type;

        this.updateProductForm();

        // 填充表单数据
        setTimeout(() => {
            if (product.type === 'redpack') {
                // 复制模式下清空时间，让用户重新设置
                document.getElementById('redpack_sendTime').value = isCopy ? '' : (product.sendTime || '');
                document.getElementById('redpack_drawTime').value = product.drawTime || '3';
                document.getElementById('redpack_currentRound').value = product.currentRound || '';
                document.getElementById('redpack_totalRounds').value = product.totalRounds || '';
                document.getElementById('redpack_participationType').value = product.participationType || 'fanclub';
                document.getElementById('redpack_coins').value = product.coins || '';
                if (document.getElementById('redpack_password')) {
                    document.getElementById('redpack_password').value = product.password || '';
                }
            } else if (product.type === 'lucky') {
                document.getElementById('lucky_condition').value = product.condition || 'comment';
                document.getElementById('lucky_winners').value = product.winners || '';
                document.getElementById('lucky_currentRound').value = product.currentRound || '';
                document.getElementById('lucky_totalRounds').value = product.totalRounds || '';
                document.getElementById('lucky_countdown').value = product.countdown || '';
                // 复制模式下清空时间，让用户重新设置
                document.getElementById('lucky_scheduledTime').value = isCopy ? '' : (product.scheduledTime || '');
                document.getElementById('lucky_prizeDescription').value = product.prizeDescription || '';
                if (document.getElementById('lucky_watchtime')) {
                    document.getElementById('lucky_watchtime').value = product.watchtime || '';
                }
            }
        }, 100);

        document.getElementById('productForm').classList.remove('hidden');

        if (isCopy) {
            this.showMessage('配置已复制，请修改时间后保存', 'info');
        }
    }

    // 删除产品
    removeProduct(productId) {
        if (confirm('确定要删除这个产品配置吗？')) {
            if (window.storageManager.removeProduct(productId)) {
                this.loadProducts();
                this.showMessage('产品配置删除成功', 'success');
                
                // 通知Dashboard更新
                if (window.dashboardManager) {
                    window.dashboardManager.updateDashboard();
                }
            } else {
                this.showMessage('删除失败', 'error');
            }
        }
    }

    // 导出配置
    exportConfig() {
        window.storageManager.exportConfig();
        this.showMessage('配置导出成功', 'success');
    }

    // 导入配置
    importConfig(file) {
        if (!file) return;
        
        window.storageManager.importConfig(file)
            .then(() => {
                this.loadRooms();
                this.loadProducts();
                this.showMessage('配置导入成功', 'success');
                
                // 通知Dashboard更新
                if (window.dashboardManager) {
                    window.dashboardManager.updateDashboard();
                }
            })
            .catch(error => {
                this.showMessage('导入失败: ' + error.message, 'error');
            });
    }

    // 获取参与方式文本
    getParticipationTypeText(type) {
        const types = {
            'fanclub': '粉丝团红包',
            'share': '分享红包',
            'condition': '条件型普通快币红包',
            'password': '口令红包'
        };
        return types[type] || type;
    }

    // 获取条件文本
    getConditionText(condition) {
        const conditions = {
            'comment': '评论',
            'like': '点赞',
            'fanclub': '粉丝团',
            'share': '分享',
            'follow': '关注主播',
            'watchtime': '观看时长',
            'superfan': '超粉团'
        };
        return conditions[condition] || condition;
    }

    // 显示批量导入模态框
    showBatchImportModal() {
        const rooms = window.storageManager.getRooms();
        const roomsText = rooms.join('、');

        const modalContent = `
            <div class="space-y-4">
                <div class="mb-4">
                    <h4 class="text-lg font-semibold text-blue-400 mb-2">批量导入产品配置</h4>
                    <p class="text-sm text-gray-300">请按照指定格式录入产品配置信息，每行一个产品配置。</p>
                    <div class="mt-2 p-2 bg-blue-900 rounded text-blue-200 text-sm">
                        <strong>📋 当前可用直播间：</strong>${roomsText}
                    </div>
                </div>

                <!-- 横向布局：左侧格式说明，右侧输入区域 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- 左侧：格式说明 -->
                    <div class="space-y-4">
                        <div>
                            <h5 class="font-semibold text-orange-400 mb-2">红包格式：</h5>
                            <div class="bg-gray-700 p-2 rounded text-xs font-mono text-gray-300 leading-tight">
                                直播间名,发送时间,开奖时间,轮次,参与方式,快币数
                            </div>
                            <div class="bg-gray-600 p-2 rounded text-xs text-yellow-300 mt-1">
                                <strong>范例：</strong>Before Party,2025-08-30 18:00:00,3min,1/4,粉丝团红包,66
                            </div>
                        </div>

                        <div>
                            <h5 class="font-semibold text-green-400 mb-2">幸运助手格式：</h5>
                            <div class="bg-gray-700 p-2 rounded text-xs font-mono text-gray-300 leading-tight">
                                直播间名,参与条件,中奖人数,轮次,倒计时,发送时间,奖品说明
                            </div>
                            <div class="bg-gray-600 p-2 rounded text-xs text-yellow-300 mt-1">
                                <strong>范例：</strong>Before Party,评论,100人,1/4,5min,2025-08-30 18:00:00,奖品
                            </div>
                        </div>

                        <div class="text-xs text-gray-400">
                            <p><strong>注意事项：</strong></p>
                            <ul class="list-disc list-inside space-y-1 mt-1">
                                <li>时间格式：2025-08-30 18:00:00</li>
                                <li>轮次格式：1/4（当前/总数）</li>
                                <li>中奖人数必须带"人"字</li>
                                <li>每行一个配置</li>
                            </ul>
                        </div>
                    </div>

                    <!-- 右侧：输入区域 -->
                    <div>
                        <label class="block text-sm font-medium mb-2">批量导入内容：</label>
                        <textarea id="batchImportText" rows="12"
                                  class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white font-mono text-sm"
                                  placeholder="请按照左侧格式输入产品配置，每行一个配置..."></textarea>
                    </div>
                </div>

                <div class="flex justify-end space-x-4 pt-4 border-t border-gray-600">
                    <button onclick="window.app.hideModal()"
                            class="bg-gray-600 hover:bg-gray-700 px-6 py-2 rounded transition-colors">
                        取消
                    </button>
                    <button onclick="configManager.processBatchImport()"
                            class="bg-green-600 hover:bg-green-700 px-6 py-2 rounded transition-colors">
                        开始导入
                    </button>
                </div>
            </div>
        `;

        window.app.showModal('批量导入', modalContent);
    }

    // 显示清空配置模态框
    showClearConfigModal() {
        const products = window.storageManager.getProducts();
        const productCount = products.length;

        if (productCount === 0) {
            this.showMessage('当前没有配置项需要清空', 'info');
            return;
        }

        const modalContent = `
            <div class="space-y-4">
                <div class="mb-4">
                    <h4 class="text-lg font-semibold text-red-400 mb-2">⚠️ 清空配置确认</h4>
                    <p class="text-sm text-gray-300">您即将清空所有产品配置，此操作不可撤销！</p>
                    <div class="mt-2 p-3 bg-red-900 rounded text-red-200 text-sm">
                        <strong>当前共有 ${productCount} 个配置项将被删除</strong>
                    </div>
                </div>

                <div class="bg-yellow-900 p-3 rounded text-yellow-200 text-sm">
                    <strong>⚠️ 安全确认：</strong>请在下方输入框中输入"确认"二字，然后点击确认按钮执行清空操作。
                </div>

                <div>
                    <label class="block text-sm font-medium mb-2">确认输入：</label>
                    <input type="text" id="clearConfirmInput"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                           placeholder="请输入"确认"二字">
                </div>

                <div class="flex justify-end space-x-4 pt-4 border-t border-gray-600">
                    <button onclick="window.app.hideModal()"
                            class="bg-gray-600 hover:bg-gray-700 px-6 py-2 rounded transition-colors">
                        取消
                    </button>
                    <button onclick="configManager.processClearConfig()"
                            class="bg-red-600 hover:bg-red-700 px-6 py-2 rounded transition-colors">
                        确认清空
                    </button>
                </div>
            </div>
        `;

        window.app.showModal('清空配置', modalContent);
    }

    // 处理清空配置
    processClearConfig() {
        const confirmInput = document.getElementById('clearConfirmInput');
        if (!confirmInput) {
            this.showMessage('确认输入框未找到', 'error');
            return;
        }

        const inputValue = confirmInput.value.trim();
        if (inputValue !== '确认') {
            this.showMessage('请输入"确认"二字以执行清空操作', 'error');
            return;
        }

        // 执行清空操作
        const products = window.storageManager.getProducts();
        const productCount = products.length;

        // 清空所有产品配置
        window.storageManager.clearAllProducts();

        // 刷新界面
        this.loadProducts();

        // 通知Dashboard更新
        if (window.dashboardManager) {
            window.dashboardManager.updateDashboard();
        }

        // 关闭模态框
        window.app.hideModal();

        // 显示成功消息
        this.showMessage(`成功清空 ${productCount} 个配置项`, 'success');
    }

    // 显示批量修改模态框
    showBatchEditModal() {
        const products = window.storageManager.getProducts();
        const rooms = window.storageManager.getRooms();
        const roomsText = rooms.join('、');

        if (products.length === 0) {
            this.showMessage('当前没有配置项可以修改', 'info');
            return;
        }

        // 将当前配置转换为批量导入格式
        const currentConfigText = this.convertProductsToImportFormat(products);

        const modalContent = `
            <div class="space-y-4">
                <div class="mb-4">
                    <h4 class="text-lg font-semibold text-indigo-400 mb-2">批量修改产品配置</h4>
                    <p class="text-sm text-gray-300">当前配置已加载到输入框中，您可以直接修改。修改完成后点击"保存修改"。</p>
                    <div class="mt-2 p-2 bg-blue-900 rounded text-blue-200 text-sm">
                        <strong>📋 当前可用直播间：</strong>${roomsText}
                    </div>
                </div>

                <!-- 横向布局：左侧格式说明，右侧输入区域 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- 左侧：格式说明 -->
                    <div class="space-y-4">
                        <div>
                            <h5 class="font-semibold text-orange-400 mb-2">红包格式：</h5>
                            <div class="bg-gray-700 p-2 rounded text-xs font-mono text-gray-300 leading-tight">
                                直播间名,发送时间,开奖时间,轮次,参与方式,快币数
                            </div>
                            <div class="bg-gray-600 p-2 rounded text-xs text-yellow-300 mt-1">
                                <strong>范例：</strong>Before Party,2025-08-30 18:00:00,3min,1/4,粉丝团红包,66
                            </div>
                        </div>

                        <div>
                            <h5 class="font-semibold text-green-400 mb-2">幸运助手格式：</h5>
                            <div class="bg-gray-700 p-2 rounded text-xs font-mono text-gray-300 leading-tight">
                                直播间名,参与条件,中奖人数,轮次,倒计时,发送时间,奖品说明
                            </div>
                            <div class="bg-gray-600 p-2 rounded text-xs text-yellow-300 mt-1">
                                <strong>范例：</strong>Before Party,评论,100人,1/4,5min,2025-08-30 18:00:00,奖品
                            </div>
                        </div>

                        <div class="text-xs text-gray-400">
                            <p><strong>修改说明：</strong></p>
                            <ul class="list-disc list-inside space-y-1 mt-1">
                                <li>直接修改输入框中的内容</li>
                                <li>可以删除不需要的行</li>
                                <li>可以添加新的配置行</li>
                                <li>保存后将替换所有当前配置</li>
                            </ul>
                        </div>
                    </div>

                    <!-- 右侧：输入区域 -->
                    <div>
                        <label class="block text-sm font-medium mb-2">当前配置（可修改）：</label>
                        <textarea id="batchEditText" rows="12"
                                  class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white font-mono text-sm"
                                  placeholder="配置内容将在这里显示...">${currentConfigText}</textarea>
                    </div>
                </div>

                <div class="flex justify-end space-x-4 pt-4 border-t border-gray-600">
                    <button onclick="window.app.hideModal()"
                            class="bg-gray-600 hover:bg-gray-700 px-6 py-2 rounded transition-colors">
                        取消
                    </button>
                    <button onclick="configManager.processBatchEdit()"
                            class="bg-indigo-600 hover:bg-indigo-700 px-6 py-2 rounded transition-colors">
                        保存修改
                    </button>
                </div>
            </div>
        `;

        window.app.showModal('批量修改', modalContent);
    }

    // 将产品配置转换为批量导入格式
    convertProductsToImportFormat(products) {
        return products.map(product => {
            if (product.type === 'redpack') {
                const participationType = this.getParticipationTypeText(product.participationType);
                return `${product.room},${product.sendTime},${product.drawTime}min,${product.currentRound}/${product.totalRounds},${participationType},${product.coins}`;
            } else if (product.type === 'lucky') {
                const condition = this.getConditionText(product.condition);
                return `${product.room},${condition},${product.winners}人,${product.currentRound}/${product.totalRounds},${product.countdown}min,${product.scheduledTime},${product.prizeDescription || '奖品'}`;
            }
            return '';
        }).filter(line => line).join('\n');
    }

    // 处理批量修改
    processBatchEdit() {
        const text = document.getElementById('batchEditText').value.trim();
        if (!text) {
            this.showMessage('请输入修改内容', 'error');
            return;
        }

        // 先清空所有现有配置
        window.storageManager.clearAllProducts();

        const lines = text.split('\n').filter(line => line.trim());
        let successCount = 0;
        let errorCount = 0;
        const errors = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) continue;

            try {
                const result = this.parseBatchImportLine(line, i + 1);
                if (result.success) {
                    const productId = window.storageManager.addProduct(result.product);
                    if (productId) {
                        successCount++;
                    } else {
                        errorCount++;
                        errors.push(`第${i + 1}行：保存失败`);
                    }
                } else {
                    errorCount++;
                    errors.push(`第${i + 1}行：${result.error}`);
                }
            } catch (error) {
                errorCount++;
                errors.push(`第${i + 1}行：解析失败 - ${error.message}`);
            }
        }

        // 显示结果
        let message = `批量修改完成：成功 ${successCount} 个`;
        if (errorCount > 0) {
            message += `，失败 ${errorCount} 个`;
        }

        if (errors.length > 0 && errors.length <= 5) {
            message += '\n错误详情：\n' + errors.join('\n');
        } else if (errors.length > 5) {
            message += '\n错误详情（前5个）：\n' + errors.slice(0, 5).join('\n') + '\n...';
        }

        this.showMessage(message, errorCount > 0 ? 'error' : 'success');

        if (successCount > 0) {
            this.loadProducts();
            // 通知Dashboard更新
            if (window.dashboardManager) {
                window.dashboardManager.updateDashboard();
            }
        }

        window.app.hideModal();
    }

    // 显示加载线上配置模态框
    showLoadOnlineConfigModal() {
        const modalContent = `
            <div class="space-y-4">
                <div class="mb-4">
                    <h4 class="text-lg font-semibold text-purple-400 mb-2">🌐 加载线上配置</h4>
                    <p class="text-sm text-gray-300">输入线上配置文件的URL地址，支持直链文件或Github raw文件。</p>
                </div>

                <div class="bg-blue-900 p-3 rounded text-blue-200 text-sm">
                    <strong>📋 支持的URL格式：</strong>
                    <ul class="list-disc list-inside mt-2 space-y-1">
                        <li>直链文件：https://example.com/config.ksconf</li>
                        <li>Github Raw：https://raw.githubusercontent.com/user/repo/main/config.ksconf</li>
                        <li>其他可直接访问的配置文件链接</li>
                    </ul>
                </div>

                <div>
                    <label class="block text-sm font-medium mb-2">配置文件URL：</label>
                    <input type="url" id="onlineConfigUrl"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                           placeholder="https://example.com/config.ksconf">
                </div>

                <div class="flex justify-end space-x-4 pt-4 border-t border-gray-600">
                    <button onclick="window.app.hideModal()"
                            class="bg-gray-600 hover:bg-gray-700 px-6 py-2 rounded transition-colors">
                        取消
                    </button>
                    <button onclick="configManager.loadOnlineConfig()"
                            class="bg-purple-600 hover:bg-purple-700 px-6 py-2 rounded transition-colors">
                        读取在线配置
                    </button>
                </div>
            </div>
        `;

        window.app.showModal('加载线上配置', modalContent);
    }

    // 加载线上配置
    async loadOnlineConfig() {
        const urlInput = document.getElementById('onlineConfigUrl');
        if (!urlInput || !urlInput.value.trim()) {
            this.showMessage('请输入配置文件URL', 'error');
            return;
        }

        const url = urlInput.value.trim();

        // 简单的URL格式验证
        try {
            new URL(url);
        } catch (error) {
            this.showMessage('请输入有效的URL地址', 'error');
            return;
        }

        try {
            this.showMessage('正在读取线上配置...', 'info');

            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const configText = await response.text();
            let configData;

            try {
                configData = JSON.parse(configText);
            } catch (parseError) {
                throw new Error('配置文件格式错误，请确保是有效的JSON格式');
            }

            // 验证配置数据格式
            if (!configData.rooms || !Array.isArray(configData.rooms)) {
                throw new Error('配置文件格式错误：缺少rooms字段');
            }

            if (!configData.products || !Array.isArray(configData.products)) {
                throw new Error('配置文件格式错误：缺少products字段');
            }

            // 导入配置
            const success = window.storageManager.importConfig(configData);
            if (success) {
                this.loadRooms();
                this.loadProducts();

                // 通知Dashboard更新
                if (window.dashboardManager) {
                    window.dashboardManager.updateDashboard();
                }

                window.app.hideModal();
                this.showMessage(`成功从线上加载配置！\n直播间：${configData.rooms.length}个\n产品配置：${configData.products.length}个`, 'success');
            } else {
                throw new Error('配置导入失败');
            }

        } catch (error) {
            console.error('加载线上配置失败:', error);
            this.showMessage(`加载线上配置失败：${error.message}`, 'error');
        }
    }

    // 处理批量导入
    processBatchImport() {
        const text = document.getElementById('batchImportText').value.trim();
        if (!text) {
            this.showMessage('请输入导入内容', 'error');
            return;
        }

        const lines = text.split('\n').filter(line => line.trim());
        let successCount = 0;
        let errorCount = 0;
        const errors = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) continue;

            try {
                const result = this.parseBatchImportLine(line, i + 1);
                if (result.success) {
                    const productId = window.storageManager.addProduct(result.product);
                    if (productId) {
                        successCount++;
                    } else {
                        errorCount++;
                        errors.push(`第${i + 1}行：保存失败`);
                    }
                } else {
                    errorCount++;
                    errors.push(`第${i + 1}行：${result.error}`);
                }
            } catch (error) {
                errorCount++;
                errors.push(`第${i + 1}行：解析失败 - ${error.message}`);
            }
        }

        // 显示结果
        let message = `导入完成：成功 ${successCount} 个`;
        if (errorCount > 0) {
            message += `，失败 ${errorCount} 个`;
        }

        if (errors.length > 0 && errors.length <= 5) {
            message += '\n错误详情：\n' + errors.join('\n');
        } else if (errors.length > 5) {
            message += '\n错误详情（前5个）：\n' + errors.slice(0, 5).join('\n') + '\n...';
        }

        this.showMessage(message, errorCount > 0 ? 'error' : 'success');

        if (successCount > 0) {
            this.loadProducts();
            // 通知Dashboard更新
            if (window.dashboardManager) {
                window.dashboardManager.updateDashboard();
            }
        }

        window.app.hideModal();
    }

    // 解析批量导入的单行数据
    parseBatchImportLine(line, lineNumber) {
        const parts = line.split(',').map(part => part.trim());

        // 判断是红包还是幸运助手
        if (parts.length === 6) {
            // 红包格式：直播间名,红包发送时间,开奖时间,轮次,参与方式,快币数
            return this.parseRedpackLine(parts, lineNumber);
        } else if (parts.length === 7) {
            // 幸运助手格式：直播间名,参与条件,中奖人数,轮次,开奖倒计时,定时发送时间,奖品说明
            return this.parseLuckyLine(parts, lineNumber);
        } else {
            return {
                success: false,
                error: `格式错误，应为6个字段（红包）或7个字段（幸运助手），实际为${parts.length}个字段`
            };
        }
    }

    // 解析红包行
    parseRedpackLine(parts, lineNumber) {
        const [room, sendTime, drawTime, rounds, participationType, coins] = parts;

        // 验证直播间
        const rooms = window.storageManager.getRooms();
        if (!rooms.includes(room)) {
            return { success: false, error: `直播间"${room}"不存在` };
        }

        // 验证时间格式
        if (!/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(sendTime)) {
            return { success: false, error: `红包发送时间格式错误，应为"2025-08-30 18:00:00"` };
        }

        // 验证开奖时间
        const drawTimeMatch = drawTime.match(/^(\d+)min$/);
        if (!drawTimeMatch || !['3', '10'].includes(drawTimeMatch[1])) {
            return { success: false, error: `开奖时间格式错误，应为"3min"或"10min"` };
        }

        // 验证轮次
        const roundsMatch = rounds.match(/^(\d+)\/(\d+)$/);
        if (!roundsMatch) {
            return { success: false, error: `轮次格式错误，应为"1/4"格式` };
        }

        // 验证参与方式
        const participationTypes = {
            '粉丝团红包': 'fanclub',
            '分享红包': 'share',
            '条件型普通快币红包': 'condition',
            '口令红包': 'password'
        };
        if (!participationTypes[participationType]) {
            return { success: false, error: `参与方式"${participationType}"不支持` };
        }

        // 验证快币数
        const coinsNum = parseInt(coins);
        if (isNaN(coinsNum) || coinsNum < 0) {
            return { success: false, error: `快币数格式错误` };
        }

        return {
            success: true,
            product: {
                room: room,
                type: 'redpack',
                sendTime: sendTime,
                drawTime: parseInt(drawTimeMatch[1]),
                currentRound: parseInt(roundsMatch[1]),
                totalRounds: parseInt(roundsMatch[2]),
                participationType: participationTypes[participationType],
                coins: coinsNum
            }
        };
    }

    // 解析幸运助手行
    parseLuckyLine(parts, lineNumber) {
        const [room, condition, winners, rounds, countdown, scheduledTime, prizeDescription] = parts;

        // 验证直播间
        const rooms = window.storageManager.getRooms();
        if (!rooms.includes(room)) {
            return { success: false, error: `直播间"${room}"不存在` };
        }

        // 验证参与条件
        const conditions = {
            '评论': 'comment',
            '点赞': 'like',
            '粉丝团': 'fanclub',
            '分享': 'share',
            '关注主播': 'follow',
            '观看时长': 'watchtime',
            '超粉团': 'superfan'
        };
        if (!conditions[condition]) {
            return { success: false, error: `参与条件"${condition}"不支持` };
        }

        // 验证中奖人数
        const winnersMatch = winners.match(/^(\d+)人$/);
        if (!winnersMatch) {
            return { success: false, error: `中奖人数格式错误，应为"100人"格式` };
        }

        // 验证轮次
        const roundsMatch = rounds.match(/^(\d+)\/(\d+)$/);
        if (!roundsMatch) {
            return { success: false, error: `轮次格式错误，应为"1/4"格式` };
        }

        // 验证开奖倒计时
        const countdownMatch = countdown.match(/^(\d+)min$/);
        if (!countdownMatch) {
            return { success: false, error: `开奖倒计时格式错误，应为"5min"格式` };
        }

        // 验证定时发送时间
        if (!/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(scheduledTime)) {
            return { success: false, error: `定时发送时间格式错误，应为"2025-08-30 18:00:00"` };
        }

        return {
            success: true,
            product: {
                room: room,
                type: 'lucky',
                condition: conditions[condition],
                winners: parseInt(winnersMatch[1]),
                currentRound: parseInt(roundsMatch[1]),
                totalRounds: parseInt(roundsMatch[2]),
                countdown: parseInt(countdownMatch[1]),
                scheduledTime: scheduledTime,
                prizeDescription: prizeDescription
            }
        };
    }

    // 滚动到产品配置表单
    scrollToProductForm() {
        setTimeout(() => {
            const productForm = document.getElementById('productForm');
            if (productForm && !productForm.classList.contains('hidden')) {
                productForm.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start',
                    inline: 'nearest'
                });
            }
        }, 100); // 延迟一点确保表单已经显示
    }

    // 滚动到产品配置列表
    scrollToProductList() {
        setTimeout(() => {
            const productList = document.getElementById('productList');
            if (productList) {
                productList.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start',
                    inline: 'nearest'
                });
            }
        }, 100); // 延迟一点确保列表已经更新
    }

    // 显示消息
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageDiv = document.createElement('div');
        messageDiv.className = `fixed bottom-4 right-4 px-6 py-3 rounded-lg text-white z-50 transition-all duration-300 transform translate-y-0 max-w-md ${
            type === 'success' ? 'bg-green-600' :
            type === 'error' ? 'bg-red-600' :
            'bg-blue-600'
        }`;

        // 处理多行消息
        if (message.includes('\n')) {
            messageDiv.innerHTML = message.split('\n').map(line => `<div>${line}</div>`).join('');
        } else {
            messageDiv.textContent = message;
        }

        // 添加关闭按钮
        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '×';
        closeBtn.className = 'ml-3 text-white hover:text-gray-200 font-bold text-lg float-right';
        closeBtn.onclick = () => {
            messageDiv.style.transform = 'translateY(100%)';
            messageDiv.style.opacity = '0';
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 300);
        };
        messageDiv.appendChild(closeBtn);

        document.body.appendChild(messageDiv);

        // 入场动画
        setTimeout(() => {
            messageDiv.style.transform = 'translateY(0)';
        }, 10);

        // 10秒后自动移除（批量导入可能有很多错误信息）
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.style.transform = 'translateY(100%)';
                messageDiv.style.opacity = '0';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 300);
            }
        }, 10000);
    }
}

// 创建全局配置管理器实例
window.configManager = new ConfigManager();
