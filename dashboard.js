// Dashboard显示管理模块
class DashboardManager {
    constructor() {
        this.updateInterval = null;
        this.carouselIndex = 0;
        this.carouselInterval = null;
        this.currentProducts = null;
        this.debugMode = false;
        this.debugTime = null;
        this.debugTimeOffset = 0; // 调试时间与真实时间的偏移量（毫秒）
        this.init();
    }

    init() {
        this.loadDebugMode(); // 加载调试模式状态
        this.bindTimeEvents(); // 绑定时间相关事件
        this.startTimeUpdate();
        this.updateDashboard();

        // 每秒更新一次下一个产品的倒计时
        setInterval(() => {
            this.updateNextProduct();
        }, 1000);

        // 每5秒更新一次完整Dashboard（提高响应速度）
        setInterval(() => {
            this.updateDashboard();
        }, 5000);
    }

    // 开始时间更新
    startTimeUpdate() {
        this.updateCurrentTime();
        setInterval(() => {
            this.updateCurrentTime();
        }, 1000);
    }

    // 绑定时间相关事件
    bindTimeEvents() {
        const currentTimeBtn = document.getElementById('currentTimeBtn');
        if (currentTimeBtn) {
            currentTimeBtn.addEventListener('click', () => {
                if (this.debugMode) {
                    this.showExitDebugModeModal();
                } else {
                    this.showDebugTimeModal();
                }
            });
        }
    }

    // 加载调试模式状态
    loadDebugMode() {
        const savedDebugMode = localStorage.getItem('debugMode');
        const savedDebugTime = localStorage.getItem('debugTime');

        if (savedDebugMode === 'true' && savedDebugTime) {
            this.debugMode = true;
            this.debugTime = new Date(savedDebugTime);
            this.debugTimeOffset = this.debugTime.getTime() - new Date().getTime();
            this.updateTimeDisplay();
        }
    }

    // 保存调试模式状态
    saveDebugMode() {
        localStorage.setItem('debugMode', this.debugMode.toString());
        if (this.debugTime) {
            localStorage.setItem('debugTime', this.debugTime.toISOString());
        } else {
            localStorage.removeItem('debugTime');
        }
    }

    // 获取当前时间（考虑调试模式）
    getCurrentTime() {
        if (this.debugMode && this.debugTime) {
            // 返回调试时间加上经过的真实时间
            const realTimeElapsed = new Date().getTime() - (this.debugTime.getTime() - this.debugTimeOffset);
            return new Date(this.debugTime.getTime() + realTimeElapsed);
        }
        return new Date();
    }

    // 更新当前时间
    updateCurrentTime() {
        const now = this.getCurrentTime();
        const timeString = now.toLocaleString('zh-CN', {
            timeZone: 'Asia/Shanghai',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });

        document.getElementById('currentTime').textContent = timeString;
        this.updateTimeDisplay();
    }

    // 更新时间显示样式
    updateTimeDisplay() {
        const timeLabel = document.getElementById('timeLabel');
        const currentTime = document.getElementById('currentTime');

        if (this.debugMode) {
            timeLabel.textContent = '调试时间：';
            currentTime.className = 'debug-time-rainbow font-bold';
        } else {
            timeLabel.textContent = '北京时间：';
            currentTime.className = '';
        }
    }

    // 更新Dashboard
    updateDashboard() {
        this.updateNextProduct();
        this.updateDashboardGrid();
        this.updateTimeline();
    }

    // 显示调试时间设置模态框
    showDebugTimeModal() {
        const modalContent = `
            <div class="space-y-4">
                <h3 class="text-lg font-semibold text-yellow-400 mb-4">设置调试时间</h3>
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">选择调试时间：</label>
                        <input type="datetime-local" id="debugTimeInput"
                               class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                               value="${new Date().toISOString().slice(0, 16)}">
                    </div>
                    <div class="text-sm text-gray-400">
                        <p>⚠️ 注意：进入调试模式后，系统内所有时间计算都将基于调试时间。</p>
                        <p>🌈 调试模式下，时间显示将呈现彩虹色渐变效果。</p>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button onclick="window.app.hideModal()"
                            class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded transition-colors">
                        取消
                    </button>
                    <button onclick="window.dashboardManager.setDebugTime()"
                            class="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 rounded transition-colors">
                        确认设置
                    </button>
                </div>
            </div>
        `;

        window.app.showModal('调试时间设置', modalContent);
    }

    // 显示退出调试模式确认框
    showExitDebugModeModal() {
        const modalContent = `
            <div class="space-y-4">
                <h3 class="text-lg font-semibold text-red-400 mb-4">退出测试模式</h3>
                <div class="text-center">
                    <div class="text-6xl mb-4">⚠️</div>
                    <p class="text-lg text-gray-300 mb-4">是否退出测试模式？</p>
                    <p class="text-sm text-gray-400">退出后将回归北京时间</p>
                </div>
                <div class="flex justify-center space-x-4 mt-6">
                    <button onclick="window.app.hideModal()"
                            class="px-6 py-2 bg-gray-600 hover:bg-gray-700 rounded transition-colors">
                        否
                    </button>
                    <button onclick="window.dashboardManager.exitDebugMode()"
                            class="px-6 py-2 bg-red-600 hover:bg-red-700 rounded transition-colors">
                        是
                    </button>
                </div>
            </div>
        `;

        window.app.showModal('退出测试模式', modalContent);
    }

    // 设置调试时间
    setDebugTime() {
        const debugTimeInput = document.getElementById('debugTimeInput');
        if (!debugTimeInput || !debugTimeInput.value) {
            alert('请选择调试时间');
            return;
        }

        const selectedTime = new Date(debugTimeInput.value);
        if (isNaN(selectedTime.getTime())) {
            alert('请选择有效的时间');
            return;
        }

        this.debugMode = true;
        this.debugTime = selectedTime;
        this.debugTimeOffset = selectedTime.getTime() - new Date().getTime();
        this.saveDebugMode();
        this.updateTimeDisplay();

        window.app.hideModal();

        // 显示成功消息
        if (window.configManager) {
            window.configManager.showMessage('调试时间设置成功！系统已进入测试模式', 'success');
        }
    }

    // 退出调试模式
    exitDebugMode() {
        this.debugMode = false;
        this.debugTime = null;
        this.debugTimeOffset = 0;
        this.saveDebugMode();
        this.updateTimeDisplay();

        window.app.hideModal();

        // 显示成功消息
        if (window.configManager) {
            window.configManager.showMessage('已退出测试模式，回归北京时间', 'success');
        }
    }

    // 更新下一个即将上线的产品信息
    updateNextProduct() {
        const nextProducts = window.storageManager.getNextProducts();
        const nextProductName = document.getElementById('nextProductName');
        const nextProductTime = document.getElementById('nextProductTime');
        const nextProductRoom = document.getElementById('nextProductRoom');
        const nextProductInfo = document.getElementById('nextProductInfo');
        const nextProductDiv = document.getElementById('nextProduct');

        if (!nextProducts || nextProducts.length === 0) {
            nextProductName.textContent = '暂无配置';
            nextProductName.className = 'text-xl font-semibold ml-2 text-gray-400';
            nextProductTime.textContent = '--:--:--';
            nextProductRoom.textContent = '直播间：--';
            nextProductInfo.textContent = '详情：--';
            nextProductTime.className = 'text-3xl font-bold font-mono text-yellow-400';
            nextProductDiv.className = 'max-w-7xl mx-auto mt-4 p-4 bg-gray-700 rounded-lg';
            this.stopCarousel();
            return;
        }

        // 如果有多个产品，启动轮播
        if (nextProducts.length > 1) {
            // 检查是否需要重新启动轮播（产品列表发生变化）
            const productsChanged = !this.currentProducts ||
                this.currentProducts.length !== nextProducts.length ||
                !this.currentProducts.every((p, i) => p.id === nextProducts[i].id);

            if (!this.carouselInterval || productsChanged) {
                this.currentProducts = [...nextProducts]; // 创建副本
                this.startCarousel(nextProducts);
            }
        } else {
            this.stopCarousel();
            this.currentProducts = null;
        }

        // 获取当前要显示的产品
        const currentIndex = nextProducts.length > 1 ? this.carouselIndex : 0;
        const nextProduct = nextProducts[currentIndex];

        if (!nextProduct) return;

        const now = this.getCurrentTime(); // 使用调试时间
        const productStatus = window.storageManager.getProductStatus(nextProduct);
        const productTypeName = nextProduct.type === 'redpack' ? '红包' : '幸运助手';

        // 更新产品名称和状态
        if (nextProducts.length > 1) {
            nextProductName.textContent = `🚨 注意同时上线 - ${productTypeName} (${nextProduct.room}) - ${productStatus.statusText}`;
            nextProductName.className = 'text-xl font-semibold ml-2 text-red-400';
        } else {
            nextProductName.textContent = `${productTypeName} (${nextProduct.room}) - ${productStatus.statusText}`;
            // 更新产品名称颜色
            if (nextProduct.type === 'redpack') {
                nextProductName.className = 'text-xl font-semibold ml-2 text-orange-400';
            } else {
                nextProductName.className = 'text-xl font-semibold ml-2 text-green-400';
            }
        }

        // 时间显示基于最早的产品（如果有多个产品）
        let timeProduct = nextProduct;
        if (nextProducts.length > 1) {
            // 找到最早的产品
            timeProduct = nextProducts.reduce((earliest, current) => {
                const earliestTime = new Date(earliest.sendTime || earliest.scheduledTime);
                const currentTime = new Date(current.sendTime || current.scheduledTime);
                return currentTime < earliestTime ? current : earliest;
            });
        }

        // 根据产品状态更新时间显示
        const timeProductStatus = window.storageManager.getProductStatus(timeProduct);
        let targetTime, timeDiff, timeStr;

        if (timeProductStatus.status === 'pending') {
            // 待上线：显示到上线时间的倒计时
            targetTime = new Date(timeProduct.sendTime || timeProduct.scheduledTime);
            timeDiff = targetTime - now;

            if (timeDiff > 0) {
                const hours = Math.floor(timeDiff / (1000 * 60 * 60));
                const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);
                timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            } else {
                timeStr = '即将上线';
            }
        } else if (timeProductStatus.status === 'online') {
            // 挂件上线中：显示到下线时间的倒计时
            targetTime = timeProductStatus.offlineTime;
            timeDiff = targetTime - now;

            if (timeDiff > 0) {
                const hours = Math.floor(timeDiff / (1000 * 60 * 60));
                const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);
                timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            } else {
                timeStr = '即将下线';
            }
        } else {
            timeStr = '已下线';
        }

        nextProductTime.textContent = timeStr;

        // 设置样式和呼吸灯效果（基于最早产品的状态）
        if (timeProductStatus.status === 'online') {
            // 挂件上线中：绿色背景，表示正在运行
            nextProductTime.className = 'text-3xl font-bold font-mono text-green-400';
            nextProductDiv.className = 'max-w-7xl mx-auto mt-4 p-4 bg-green-900 border-2 border-green-500 rounded-lg';
        } else if (timeProductStatus.status === 'pending' && timeDiff <= 60000) {
            // 即将上线（1分钟内）：红色呼吸灯
            nextProductTime.className = 'text-3xl font-bold font-mono text-red-500 breathing-text';
            nextProductDiv.className = 'max-w-7xl mx-auto mt-4 p-4 bg-red-900 border-2 border-red-500 rounded-lg breathing-container pulse-red';
        } else {
            // 正常状态
            nextProductTime.className = 'text-3xl font-bold font-mono text-yellow-400';
            nextProductDiv.className = 'max-w-7xl mx-auto mt-4 p-4 bg-gray-700 rounded-lg';
        }

        // 更新详细信息
        nextProductRoom.textContent = `直播间：${nextProduct.room}`;

        let details = '';
        if (nextProduct.type === 'redpack') {
            const participationType = this.getParticipationTypeText(nextProduct.participationType);
            details = `第${nextProduct.currentRound}/${nextProduct.totalRounds}轮 | ${nextProduct.coins}快币 | ${participationType} | 开奖时间：${nextProduct.drawTime}分钟`;
            if (productStatus.status === 'online') {
                details += ` | 开奖倒计时：${nextProduct.drawTime}分钟`;
            }
        } else if (nextProduct.type === 'lucky') {
            const roundInfo = nextProduct.currentRound && nextProduct.totalRounds ?
                `第${nextProduct.currentRound}/${nextProduct.totalRounds}轮 | ` : '';
            details = `${roundInfo}${nextProduct.winners}人中奖 | ${this.getConditionText(nextProduct.condition)}`;
            if (productStatus.status === 'online') {
                details += ` | 开奖倒计时：${nextProduct.countdown}分钟`;
            }
        }
        nextProductInfo.textContent = `详情：${details}`;
    }

    // 更新Dashboard网格
    updateDashboardGrid() {
        const dashboardProducts = window.storageManager.getDashboardProducts();
        const dashboardGrid = document.getElementById('dashboardGrid');

        if (dashboardProducts.length === 0) {
            dashboardGrid.innerHTML = `
                <div class="col-span-full text-center py-12">
                    <div class="text-6xl mb-4">📅</div>
                    <div class="text-xl text-gray-400">暂无需要关注的产品</div>
                    <div class="text-sm text-gray-500 mt-2">请在配置管理中添加产品配置</div>
                </div>
            `;
            return;
        }

        // 按直播间分组
        const groupedProducts = this.groupProductsByRoom(dashboardProducts);

        dashboardGrid.innerHTML = Object.entries(groupedProducts).map(([room, products]) => {
            return this.createRoomDashboardCard(room, products);
        }).join('');
    }

    // 按直播间分组产品
    groupProductsByRoom(products) {
        return products.reduce((groups, product) => {
            if (!groups[product.room]) {
                groups[product.room] = [];
            }
            groups[product.room].push(product);
            return groups;
        }, {});
    }

    // 创建直播间Dashboard卡片
    createRoomDashboardCard(room, products) {
        const now = this.getCurrentTime(); // 使用调试时间

        // 统计不同状态的产品数量
        const statusCounts = products.reduce((counts, product) => {
            const status = window.storageManager.getProductStatus(product);
            counts[status.status] = (counts[status.status] || 0) + 1;
            return counts;
        }, {});

        const totalCount = products.length;
        const onlineCount = statusCounts.online || 0;
        const pendingCount = statusCounts.pending || 0;

        return `
            <div class="bg-gray-800 border border-gray-600 rounded-lg p-6 transition-all duration-300">
                <div class="text-center mb-4">
                    <h3 class="text-2xl font-bold text-blue-400 mb-2">${room}</h3>
                    <div class="text-sm text-gray-400">
                        ${totalCount} 个产品
                        ${onlineCount > 0 ? `| <span class="text-green-400">${onlineCount} 个上线中</span>` : ''}
                        ${pendingCount > 0 ? `| <span class="text-yellow-400">${pendingCount} 个待上线</span>` : ''}
                    </div>
                </div>

                <div class="space-y-3">
                    ${products.slice(0, 3).map(product => {
                        const productStatus = window.storageManager.getProductStatus(product);
                        const productTypeName = product.type === 'redpack' ? '红包' : '幸运助手';

                        // 计算显示时间
                        let targetTime, timeDiff, timeStr;
                        if (productStatus.status === 'pending') {
                            targetTime = new Date(product.sendTime || product.scheduledTime);
                            timeDiff = targetTime - now;
                            timeStr = this.formatTimeRemaining(timeDiff);
                        } else if (productStatus.status === 'online') {
                            targetTime = productStatus.offlineTime;
                            timeDiff = targetTime - now;
                            timeStr = timeDiff > 0 ? `${Math.ceil(timeDiff / (1000 * 60))}分钟后下线` : '即将下线';
                        } else {
                            timeStr = '已下线';
                        }

                        // 单个产品的样式逻辑
                        let productCardClass = 'bg-gray-700 rounded p-3';
                        let timeClass = 'text-sm font-mono text-yellow-400';

                        if (productStatus.status === 'online') {
                            // 挂件上线中：绿色背景
                            productCardClass = 'bg-green-900 border-2 border-green-500 rounded p-3';
                            timeClass = 'text-sm font-mono text-green-300';
                        } else if (productStatus.status === 'pending' && timeDiff <= 300000 && timeDiff > 0) {
                            // 即将上线（5分钟内）
                            if (timeDiff <= 60000) {
                                // 1分钟内：红色呼吸灯
                                productCardClass = 'bg-red-900 border-2 border-red-500 rounded p-3 breathing-container pulse-red';
                                timeClass = 'text-sm font-mono text-red-400 font-bold breathing-text';
                            } else {
                                // 5分钟内：橙色警告
                                productCardClass = 'bg-orange-900 border-2 border-orange-500 rounded p-3';
                                timeClass = 'text-sm font-mono text-orange-300';
                            }
                        }

                        return `
                            <div class="${productCardClass}">
                                <div class="flex justify-between items-center mb-1">
                                    <div class="flex items-center space-x-2">
                                        <span class="font-semibold ${product.type === 'redpack' ? 'text-orange-400' : 'text-green-400'}">
                                            ${productTypeName}
                                        </span>
                                        <span class="text-xs px-2 py-1 rounded ${
                                            productStatus.status === 'online' ? 'bg-green-600' :
                                            productStatus.status === 'pending' ? 'bg-yellow-600' : 'bg-gray-600'
                                        } text-white">
                                            ${productStatus.statusText}
                                        </span>
                                    </div>
                                    <span class="${timeClass}">
                                        ${timeStr}
                                    </span>
                                </div>
                                <div class="text-xs text-gray-400">
                                    ${this.getProductSummary(product)}
                                </div>
                            </div>
                        `;
                    }).join('')}

                    ${products.length > 3 ? `
                        <div class="text-center text-sm text-gray-400">
                            还有 ${products.length - 3} 个产品...
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    // 更新时间线
    updateTimeline() {
        const allProducts = window.storageManager.getProductsByTime();
        const timelineList = document.getElementById('timelineList');

        // 按状态重新排序：非已下线产品按时间排序，已下线产品置底
        const products = allProducts.sort((a, b) => {
            const statusA = window.storageManager.getProductStatus(a);
            const statusB = window.storageManager.getProductStatus(b);

            // 如果其中一个是已下线状态，将其排到后面
            if (statusA.status === 'offline' && statusB.status !== 'offline') {
                return 1; // a排在后面
            }
            if (statusB.status === 'offline' && statusA.status !== 'offline') {
                return -1; // b排在后面
            }

            // 都是已下线状态或都不是已下线状态，按时间排序
            const timeA = new Date(a.sendTime || a.scheduledTime);
            const timeB = new Date(b.sendTime || b.scheduledTime);
            return timeA - timeB;
        });
        
        if (products.length === 0) {
            timelineList.innerHTML = `
                <div class="text-center py-8 text-gray-400">
                    <div class="text-4xl mb-4">📋</div>
                    <div>暂无产品配置</div>
                </div>
            `;
            return;
        }
        
        const now = this.getCurrentTime(); // 使用调试时间

        timelineList.innerHTML = products.map((product, index) => {
            const productStatus = window.storageManager.getProductStatus(product);
            const productTime = new Date(product.sendTime || product.scheduledTime);
            const productTypeName = product.type === 'redpack' ? '红包' : '幸运助手';

            // 根据产品状态设置样式和指示器
            let statusClass = '';
            let statusIndicator = '';
            let timeDisplayText = '';
            let timeClass = 'text-gray-400';

            if (productStatus.status === 'pending') {
                const timeDiff = productTime - now;
                const isUrgent = timeDiff <= 300000 && timeDiff > 0; // 5分钟内
                const isVeryUrgent = timeDiff <= 60000 && timeDiff > 0; // 1分钟内

                if (isVeryUrgent) {
                    statusClass = 'bg-red-900 border-l-4 border-red-500 breathing-container pulse-red';
                    statusIndicator = '🚨 准备上线';
                    timeClass = 'text-red-400 font-bold breathing-text';
                } else if (isUrgent) {
                    statusClass = 'bg-orange-900 border-l-4 border-orange-500';
                    statusIndicator = '⚠️ 即将上线';
                    timeClass = 'text-orange-400';
                } else {
                    statusClass = 'bg-gray-700 border-l-4 border-blue-500';
                    statusIndicator = '📅 待上线';
                }
                timeDisplayText = this.formatTimeRemaining(timeDiff);
            } else if (productStatus.status === 'online') {
                statusClass = 'bg-green-900 border-l-4 border-green-500';
                statusIndicator = productStatus.statusText;
                const offlineTimeDiff = productStatus.offlineTime - now;
                timeDisplayText = offlineTimeDiff > 0 ? `${Math.ceil(offlineTimeDiff / (1000 * 60))}分钟后下线` : '即将下线';
                timeClass = 'text-green-400';
            } else {
                statusClass = 'bg-gray-600 border-l-4 border-gray-500 opacity-60';
                statusIndicator = '✓ 挂件已下线';
                timeDisplayText = '已完成';
                timeClass = 'text-gray-500';
            }

            const timeStr = productTime.toLocaleString('zh-CN');

            return `
                <div class="${statusClass} rounded-lg p-4 transition-all duration-300">
                    <div class="flex justify-between items-start mb-2">
                        <div class="flex items-center space-x-3">
                            <div class="text-2xl font-bold text-gray-400">${index + 1}</div>
                            <div>
                                <div class="flex items-center space-x-2 mb-1">
                                    <span class="text-lg font-semibold text-blue-400">${product.room}</span>
                                    <span class="px-2 py-1 rounded text-xs ${product.type === 'redpack' ? 'bg-orange-600' : 'bg-green-600'} text-white">
                                        ${productTypeName}
                                    </span>
                                    <span class="text-xs px-2 py-1 rounded ${
                                        productStatus.status === 'online' ? 'bg-green-600' :
                                        productStatus.status === 'pending' ? 'bg-yellow-600' : 'bg-gray-600'
                                    } text-white">
                                        ${statusIndicator}
                                    </span>
                                </div>
                                <div class="text-sm text-gray-300">${this.getProductSummary(product)}</div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="font-mono text-yellow-400">${timeStr}</div>
                            <div class="text-sm ${timeClass}">
                                ${timeDisplayText}
                            </div>
                        </div>
                    </div>

                    ${productStatus.status === 'pending' && (productStatus.status === 'pending') ? `
                        <div class="mt-3 p-2 bg-yellow-900 rounded text-yellow-200 text-sm">
                            <strong>⚠️ 提醒：</strong>请及时检查配置系统和直播间设置
                        </div>
                    ` : ''}

                    ${productStatus.status === 'online' ? `
                        <div class="mt-3 p-2 bg-green-900 rounded text-green-200 text-sm">
                            <strong>🟢 运行中：</strong>挂件正在直播间中运行，请关注开奖情况
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');
    }

    // 格式化剩余时间
    formatTimeRemaining(timeDiff) {
        if (timeDiff < 0) {
            return '已过期';
        }

        const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

        if (days > 0) {
            return `${days}天${hours}小时 之后`;
        } else if (hours > 0) {
            return `${hours}小时${minutes}分钟 之后`;
        } else if (minutes > 0) {
            return `${minutes}分钟 之后`;
        } else {
            return `${seconds}秒 之后`;
        }
    }

    // 获取产品摘要
    getProductSummary(product) {
        if (product.type === 'redpack') {
            return `第${product.currentRound}/${product.totalRounds}轮 | ${product.coins}快币 | ${this.getParticipationTypeText(product.participationType)} | 开奖时间：${product.drawTime}分钟`;
        } else if (product.type === 'lucky') {
            const roundInfo = product.currentRound && product.totalRounds ?
                `第${product.currentRound}/${product.totalRounds}轮 | ` : '';
            return `${roundInfo}${product.winners}人中奖 | ${this.getConditionText(product.condition)} | ${product.countdown}分钟倒计时`;
        }
        return '';
    }



    // 获取参与方式文本
    getParticipationTypeText(type) {
        const types = {
            'fanclub': '粉丝团红包',
            'share': '分享红包',
            'condition': '条件型普通快币红包',
            'password': '口令红包'
        };
        return types[type] || type;
    }

    // 获取条件文本
    getConditionText(condition) {
        const conditions = {
            'comment': '评论',
            'like': '点赞',
            'fanclub': '粉丝团',
            'share': '分享',
            'follow': '关注主播',
            'watchtime': '观看时长',
            'superfan': '超粉团'
        };
        return conditions[condition] || condition;
    }

    // 启动轮播
    startCarousel(products) {
        if (this.carouselInterval) {
            clearInterval(this.carouselInterval);
        }

        // 重置索引
        this.carouselIndex = 0;

        this.carouselInterval = setInterval(() => {
            this.carouselIndex = (this.carouselIndex + 1) % products.length;
            // 只更新产品信息，不更新时间
            this.updateProductInfo(products[this.carouselIndex], products.length > 1);
        }, 5000);
    }

    // 停止轮播
    stopCarousel() {
        if (this.carouselInterval) {
            clearInterval(this.carouselInterval);
            this.carouselInterval = null;
        }
        this.carouselIndex = 0;
    }

    // 更新产品信息（不包括时间）
    updateProductInfo(product, isMultiple) {
        const nextProductName = document.getElementById('nextProductName');
        const nextProductRoom = document.getElementById('nextProductRoom');
        const nextProductInfo = document.getElementById('nextProductInfo');

        const productStatus = window.storageManager.getProductStatus(product);
        const productTypeName = product.type === 'redpack' ? '红包' : '幸运助手';

        // 更新产品名称和状态
        if (isMultiple) {
            nextProductName.textContent = `🚨 注意同时上线 - ${productTypeName} (${product.room}) - ${productStatus.statusText}`;
            nextProductName.className = 'text-xl font-semibold ml-2 text-red-400';
        } else {
            nextProductName.textContent = `${productTypeName} (${product.room}) - ${productStatus.statusText}`;
            // 更新产品名称颜色
            if (product.type === 'redpack') {
                nextProductName.className = 'text-xl font-semibold ml-2 text-orange-400';
            } else {
                nextProductName.className = 'text-xl font-semibold ml-2 text-green-400';
            }
        }

        // 更新详细信息
        nextProductRoom.textContent = `直播间：${product.room}`;

        let details = '';
        if (product.type === 'redpack') {
            const participationType = this.getParticipationTypeText(product.participationType);
            details = `第${product.currentRound}/${product.totalRounds}轮 | ${product.coins}快币 | ${participationType} | 开奖时间：${product.drawTime}分钟`;
            if (productStatus.status === 'online') {
                details += ` | 开奖倒计时：${product.drawTime}分钟`;
            }
        } else if (product.type === 'lucky') {
            const roundInfo = product.currentRound && product.totalRounds ?
                `第${product.currentRound}/${product.totalRounds}轮 | ` : '';
            details = `${roundInfo}${product.winners}人中奖 | ${this.getConditionText(product.condition)}`;
            if (productStatus.status === 'online') {
                details += ` | 开奖倒计时：${product.countdown}分钟`;
            }
        }
        nextProductInfo.textContent = `详情：${details}`;
    }

    // 手动刷新Dashboard
    refresh() {
        this.updateDashboard();
    }
}

// 创建全局Dashboard管理器实例
window.dashboardManager = new DashboardManager();
