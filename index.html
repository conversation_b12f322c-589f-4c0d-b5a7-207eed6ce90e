<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直播间功能配置上线提醒大屏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'breathing': 'breathing 2s ease-in-out infinite',
                    },
                    keyframes: {
                        breathing: {
                            '0%, 100%': { opacity: '1' },
                            '50%': { opacity: '0.3' },
                        }
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .breathing-text {
            animation: breathing 2s ease-in-out infinite;
        }
        .breathing-container {
            animation: breathing 2s ease-in-out infinite;
        }
        @keyframes pulse-red {
            0%, 100% {
                box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
            }
            50% {
                box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
            }
        }
        .pulse-red {
            animation: pulse-red 2s infinite;
        }

        /* 头部冻结模式样式 */
        .header-frozen {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(31, 41, 55, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(75, 85, 99, 0.5);
        }

        .main-content-with-frozen-header {
            /* 动态设置padding-top，通过JavaScript计算 */
        }

        /* 调试时间彩虹渐变样式 */
        .debug-time-rainbow {
            background: linear-gradient(45deg,
                #ff0000, #ff7f00, #ffff00, #00ff00,
                #0000ff, #4b0082, #9400d3, #ff0000);
            background-size: 400% 400%;
            animation: rainbow-gradient 3s ease infinite;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        @keyframes rainbow-gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 下拉菜单样式 */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #374151;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
            border-radius: 0.5rem;
            border: 1px solid #4B5563;
            top: 100%;
            right: 0;
        }

        .dropdown-content a {
            color: #E5E7EB;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            transition: background-color 0.2s;
        }

        .dropdown-content a:hover {
            background-color: #4B5563;
        }

        .dropdown-content a:first-child {
            border-radius: 0.5rem 0.5rem 0 0;
        }

        .dropdown-content a:last-child {
            border-radius: 0 0 0.5rem 0.5rem;
        }

        .dropdown:hover .dropdown-content {
            display: block;
        }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- 顶部信息栏 -->
    <header id="mainHeader" class="bg-gray-800 border-b border-gray-700 p-4 header-frozen">
        <div class="max-w-7xl mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-8">
                <h1 class="text-2xl font-bold text-blue-400">直播间功能配置上线提醒大屏</h1>
                <div class="text-lg">
                    <span id="timeLabel" class="text-gray-400">北京时间：</span>
                    <button id="currentTimeBtn" class="font-mono text-green-400 hover:text-green-300 transition-colors cursor-pointer bg-transparent border-none">
                        <span id="currentTime"></span>
                    </button>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <button id="configBtn" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors">
                    配置管理
                </button>
                <button id="dashboardBtn" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg transition-colors">
                    提醒大屏
                </button>
            </div>
        </div>

        <!-- 下一个即将上线的产品信息 -->
        <div id="nextProduct" class="max-w-7xl mx-auto mt-4 p-4 bg-gray-700 rounded-lg">
            <div class="flex items-center justify-between">
                <div>
                    <span class="text-gray-400">下一个即将上线：</span>
                    <span id="nextProductName" class="text-xl font-semibold ml-2 text-gray-400">暂无配置</span>
                </div>
                <div id="nextProductTime" class="text-3xl font-bold font-mono text-yellow-400">
                    --:--:--
                </div>
            </div>
            <div id="nextProductDetails" class="mt-2 text-sm text-gray-300">
                <span id="nextProductRoom" class="mr-4">直播间：--</span>
                <span id="nextProductInfo">详情：--</span>
            </div>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main id="mainContent" class="max-w-7xl mx-auto p-6 main-content-with-frozen-header">
        <!-- 配置界面 -->
        <div id="configSection" class="hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 直播间管理 -->
                <div class="bg-gray-800 rounded-lg p-6">
                    <h2 class="text-xl font-semibold mb-4 text-blue-400">直播间管理</h2>
                    <div class="space-y-4">
                        <div class="flex space-x-2">
                            <input type="text" id="newRoomName" placeholder="输入直播间名称" 
                                   class="flex-1 bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                            <button id="addRoomBtn" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded transition-colors">
                                添加
                            </button>
                        </div>
                        <div id="roomsList" class="space-y-2">
                            <!-- 直播间列表将在这里动态生成 -->
                        </div>
                    </div>
                </div>

                <!-- 产品配置 -->
                <div class="bg-gray-800 rounded-lg p-6">
                    <h2 class="text-xl font-semibold mb-4 text-green-400">产品配置</h2>
                    <div class="space-y-4">
                        <select id="selectedRoom" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                            <option value="">选择直播间</option>
                        </select>
                        <div class="flex space-x-2">
                            <select id="productType" class="flex-1 bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                                <option value="">选择产品类型</option>
                                <option value="redpack">红包</option>
                                <option value="lucky">幸运助手</option>
                            </select>
                            <button id="addProductBtn" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded transition-colors">
                                添加产品
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 产品配置表单 -->
            <div id="productForm" class="hidden mt-6 bg-gray-800 rounded-lg p-6">
                <!-- 表单内容将根据产品类型动态生成 -->
            </div>

            <!-- 配置列表 -->
            <div id="configListSection" class="mt-6 bg-gray-800 rounded-lg p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-purple-400">当前配置</h2>
                    <div class="space-x-2">
                        <button id="saveConfigBtn" class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded transition-colors">
                            保存配置
                        </button>
                        <div class="dropdown">
                            <button class="bg-orange-600 hover:bg-orange-700 px-4 py-2 rounded transition-colors">
                                加载配置
                            </button>
                            <div class="dropdown-content">
                                <a href="#" id="loadLocalConfigBtn">本地文件</a>
                                <a href="#" id="loadOnlineConfigBtn">线上配置</a>
                            </div>
                        </div>
                        <button id="batchImportBtn" class="bg-cyan-600 hover:bg-cyan-700 px-4 py-2 rounded transition-colors">
                            批量导入
                        </button>
                        <button id="batchEditBtn" class="bg-indigo-600 hover:bg-indigo-700 px-4 py-2 rounded transition-colors">
                            批量修改
                        </button>
                        <button id="clearConfigBtn" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded transition-colors">
                            清空配置
                        </button>
                        <input type="file" id="loadConfigFile" accept=".ksconf" class="hidden">
                    </div>
                </div>
                <div id="configList" class="space-y-3">
                    <!-- 配置项列表将在这里动态生成 -->
                </div>
            </div>
        </div>

        <!-- 提醒界面 -->
        <div id="dashboardSection">
            <!-- Dashboard区 -->
            <div class="mb-8">
                <h2 class="text-2xl font-semibold mb-6 text-center text-yellow-400">核心提醒 Dashboard</h2>
                <div id="dashboardGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Dashboard卡片将在这里动态生成 -->
                </div>
            </div>

            <!-- 详细信息区 -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-cyan-400">详细时间线</h2>
                <div id="timelineList" class="space-y-3">
                    <!-- 时间线列表将在这里动态生成 -->
                </div>
            </div>
        </div>
    </main>

    <!-- 模态框 -->
    <div id="modal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-gray-800 rounded-lg p-6 max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-4">
                <h3 id="modalTitle" class="text-lg font-semibold"></h3>
                <button id="closeModal" class="text-gray-400 hover:text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div id="modalContent"></div>
        </div>
    </div>

    <script src="storage.js"></script>
    <script src="config.js"></script>
    <script src="dashboard.js"></script>
    <script src="script.js"></script>
</body>
</html>
