// 主应用脚本
class App {
    constructor() {
        this.currentView = 'dashboard'; // 'dashboard' 或 'config'
        this.init();
    }

    init() {
        this.bindEvents();
        this.showDashboard();

        // 页面加载完成后初始化所有管理器
        document.addEventListener('DOMContentLoaded', () => {
            this.initializeManagers();
            this.adjustMainContentPadding(); // 调整主内容区域的padding
        });

        // 如果DOM已经加载完成，立即初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.initializeManagers();
                this.adjustMainContentPadding();
            });
        } else {
            this.initializeManagers();
            this.adjustMainContentPadding();
        }

        // 监听窗口大小变化，重新调整padding
        window.addEventListener('resize', () => {
            this.adjustMainContentPadding();
        });
    }

    initializeManagers() {
        // 确保所有管理器都已初始化
        if (!window.storageManager) {
            window.storageManager = new StorageManager();
        }
        if (!window.configManager) {
            window.configManager = new ConfigManager();
        }
        if (!window.dashboardManager) {
            window.dashboardManager = new DashboardManager();
        }
        
        // 初始化完成后更新显示
        this.updateView();
    }

    bindEvents() {
        // 导航按钮事件
        document.getElementById('configBtn').addEventListener('click', () => {
            this.showConfig();
        });

        document.getElementById('dashboardBtn').addEventListener('click', () => {
            this.showDashboard();
        });

        // 模态框事件
        document.getElementById('closeModal').addEventListener('click', () => {
            this.hideModal();
        });

        document.getElementById('modal').addEventListener('click', (e) => {
            if (e.target.id === 'modal') {
                this.hideModal();
            }
        });

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideModal();
            }
            
            // 快捷键
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case '1':
                        e.preventDefault();
                        this.showDashboard();
                        break;
                    case '2':
                        e.preventDefault();
                        this.showConfig();
                        break;
                    case 's':
                        e.preventDefault();
                        if (this.currentView === 'config') {
                            window.configManager.exportConfig();
                        }
                        break;
                    case 'r':
                        e.preventDefault();
                        this.refresh();
                        break;
                }
            }
        });

        // 窗口焦点事件 - 当用户切换回页面时刷新数据
        window.addEventListener('focus', () => {
            this.refresh();
        });

        // 页面可见性变化事件
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.refresh();
            }
        });
    }

    // 显示Dashboard
    showDashboard() {
        this.currentView = 'dashboard';
        this.updateView();
        this.updateNavButtons();
        
        // 刷新Dashboard数据
        if (window.dashboardManager) {
            window.dashboardManager.refresh();
        }
    }

    // 显示配置界面
    showConfig() {
        this.currentView = 'config';
        this.updateView();
        this.updateNavButtons();
        
        // 刷新配置数据
        if (window.configManager) {
            window.configManager.loadRooms();
            window.configManager.loadProducts();
        }
    }

    // 更新视图显示
    updateView() {
        const configSection = document.getElementById('configSection');
        const dashboardSection = document.getElementById('dashboardSection');
        
        if (this.currentView === 'config') {
            configSection.classList.remove('hidden');
            dashboardSection.classList.add('hidden');
        } else {
            configSection.classList.add('hidden');
            dashboardSection.classList.remove('hidden');
        }
    }

    // 更新导航按钮状态
    updateNavButtons() {
        const configBtn = document.getElementById('configBtn');
        const dashboardBtn = document.getElementById('dashboardBtn');

        if (this.currentView === 'config') {
            configBtn.className = 'bg-blue-700 px-4 py-2 rounded-lg transition-colors';
            dashboardBtn.className = 'bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg transition-colors';
        } else {
            configBtn.className = 'bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors';
            dashboardBtn.className = 'bg-green-700 px-4 py-2 rounded-lg transition-colors';
        }
    }

    // 调整主内容区域的padding以适应固定头部
    adjustMainContentPadding() {
        const header = document.getElementById('mainHeader');
        const mainContent = document.getElementById('mainContent');

        if (header && mainContent) {
            // 获取头部的实际高度
            const headerHeight = header.offsetHeight;
            // 设置主内容区域的上边距，额外添加一些间距
            mainContent.style.paddingTop = `${headerHeight + 20}px`;
        }
    }

    // 显示模态框
    showModal(title, content) {
        document.getElementById('modalTitle').textContent = title;
        document.getElementById('modalContent').innerHTML = content;
        document.getElementById('modal').classList.remove('hidden');
    }

    // 隐藏模态框
    hideModal() {
        document.getElementById('modal').classList.add('hidden');
    }

    // 刷新当前视图
    refresh() {
        if (this.currentView === 'dashboard') {
            if (window.dashboardManager) {
                window.dashboardManager.refresh();
            }
        } else {
            if (window.configManager) {
                window.configManager.loadRooms();
                window.configManager.loadProducts();
            }
        }
    }

    // 显示帮助信息
    showHelp() {
        const helpContent = `
            <div class="space-y-4">
                <div>
                    <h4 class="font-semibold text-blue-400 mb-2">快捷键</h4>
                    <ul class="text-sm space-y-1 text-gray-300">
                        <li><kbd class="bg-gray-600 px-2 py-1 rounded text-xs">Ctrl/Cmd + 1</kbd> - 切换到提醒大屏</li>
                        <li><kbd class="bg-gray-600 px-2 py-1 rounded text-xs">Ctrl/Cmd + 2</kbd> - 切换到配置管理</li>
                        <li><kbd class="bg-gray-600 px-2 py-1 rounded text-xs">Ctrl/Cmd + S</kbd> - 导出配置</li>
                        <li><kbd class="bg-gray-600 px-2 py-1 rounded text-xs">Ctrl/Cmd + R</kbd> - 刷新数据</li>
                        <li><kbd class="bg-gray-600 px-2 py-1 rounded text-xs">Esc</kbd> - 关闭模态框</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold text-green-400 mb-2">功能说明</h4>
                    <ul class="text-sm space-y-1 text-gray-300">
                        <li>• 配置管理：添加和管理直播间及产品配置</li>
                        <li>• 提醒大屏：实时显示即将上线的产品信息</li>
                        <li>• 时间提醒：剩余1分钟时显示红色呼吸灯效果</li>
                        <li>• 数据持久化：配置自动保存到浏览器本地存储</li>
                        <li>• 导入导出：支持.ksconf格式的配置文件</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold text-yellow-400 mb-2">注意事项</h4>
                    <ul class="text-sm space-y-1 text-gray-300">
                        <li>• 幸运星活动时间间隔必须大于1分钟</li>
                        <li>• 观看时长类幸运星，开奖倒计时必须大于观看时长</li>
                        <li>• 红包开奖时间只能选择3分钟或10分钟</li>
                        <li>• 所有时间均为北京时间</li>
                    </ul>
                </div>
            </div>
        `;
        
        this.showModal('使用帮助', helpContent);
    }

    // 显示关于信息
    showAbout() {
        const aboutContent = `
            <div class="text-center space-y-4">
                <div class="text-4xl mb-4">📺</div>
                <h3 class="text-xl font-semibold text-blue-400">直播间功能配置上线提醒大屏</h3>
                <div class="text-gray-300">
                    <p class="mb-2">版本：1.0.0</p>
                    <p class="mb-2">构建时间：${new Date().toLocaleDateString('zh-CN')}</p>
                </div>
                <div class="text-sm text-gray-400">
                    <p>专为直播间功能配置管理和上线提醒而设计</p>
                    <p>支持红包和幸运助手产品的配置管理</p>
                </div>
            </div>
        `;
        
        this.showModal('关于', aboutContent);
    }

    // 检查浏览器兼容性
    checkCompatibility() {
        const features = {
            localStorage: typeof Storage !== 'undefined',
            fetch: typeof fetch !== 'undefined',
            es6: typeof Symbol !== 'undefined',
            flexbox: CSS.supports('display', 'flex')
        };
        
        const unsupported = Object.entries(features)
            .filter(([feature, supported]) => !supported)
            .map(([feature]) => feature);
        
        if (unsupported.length > 0) {
            this.showModal('浏览器兼容性警告', `
                <div class="text-yellow-400">
                    <p class="mb-4">您的浏览器可能不支持以下功能：</p>
                    <ul class="list-disc list-inside space-y-1">
                        ${unsupported.map(feature => `<li>${feature}</li>`).join('')}
                    </ul>
                    <p class="mt-4 text-sm text-gray-300">
                        建议使用最新版本的 Chrome、Firefox、Safari 或 Edge 浏览器。
                    </p>
                </div>
            `);
        }
    }

    // 应用启动
    start() {
        console.log('直播间功能配置上线提醒大屏启动');
        
        // 检查浏览器兼容性
        this.checkCompatibility();
        
        // 显示启动消息
        setTimeout(() => {
            if (window.configManager) {
                window.configManager.showMessage('系统启动成功', 'success');
            }
        }, 1000);
    }
}

// 创建并启动应用
window.app = new App();

// 页面加载完成后启动应用
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.app.start();
    });
} else {
    window.app.start();
}

// 添加全局错误处理
window.addEventListener('error', (e) => {
    console.error('应用错误:', e.error);
    if (window.configManager) {
        window.configManager.showMessage('系统发生错误，请刷新页面重试', 'error');
    }
});

// 添加未处理的Promise拒绝处理
window.addEventListener('unhandledrejection', (e) => {
    console.error('未处理的Promise拒绝:', e.reason);
    if (window.configManager) {
        window.configManager.showMessage('系统发生错误，请检查网络连接', 'error');
    }
});

// 导出全局函数供HTML中使用
window.showHelp = () => window.app.showHelp();
window.showAbout = () => window.app.showAbout();
