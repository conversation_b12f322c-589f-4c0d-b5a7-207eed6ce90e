<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能测试页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .debug-time-rainbow {
            background: linear-gradient(45deg, 
                #ff0000, #ff7f00, #ffff00, #00ff00, 
                #0000ff, #4b0082, #9400d3, #ff0000);
            background-size: 400% 400%;
            animation: rainbow-gradient 3s ease infinite;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        @keyframes rainbow-gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .header-frozen {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(31, 41, 55, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(75, 85, 99, 0.5);
        }
        
        .main-content-with-frozen-header {
            /* 动态设置padding-top */
        }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- 测试头部冻结 -->
    <header class="bg-gray-800 border-b border-gray-700 p-4 header-frozen">
        <div class="max-w-7xl mx-auto">
            <h1 class="text-2xl font-bold text-blue-400">功能测试 - 头部冻结模式</h1>
            <div class="mt-2">
                <span id="timeLabel" class="text-gray-400">北京时间：</span>
                <button id="timeBtn" class="font-mono text-green-400 hover:text-green-300 transition-colors cursor-pointer bg-transparent border-none">
                    <span id="currentTime">2024-08-24 15:30:45</span>
                </button>
            </div>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="max-w-7xl mx-auto p-6 main-content-with-frozen-header">
        <div class="space-y-8">
            <!-- 功能1测试：头部冻结 -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-blue-400">功能1：头部冻结模式测试</h2>
                <p class="text-gray-300 mb-4">✅ 头部已设置为固定位置，滚动页面时头部保持不变</p>
                <div class="h-96 bg-gray-700 rounded p-4 overflow-y-auto">
                    <p class="mb-4">滚动测试内容...</p>
                    <div class="space-y-4">
                        <div class="h-20 bg-gray-600 rounded flex items-center justify-center">内容块 1</div>
                        <div class="h-20 bg-gray-600 rounded flex items-center justify-center">内容块 2</div>
                        <div class="h-20 bg-gray-600 rounded flex items-center justify-center">内容块 3</div>
                        <div class="h-20 bg-gray-600 rounded flex items-center justify-center">内容块 4</div>
                        <div class="h-20 bg-gray-600 rounded flex items-center justify-center">内容块 5</div>
                    </div>
                </div>
            </div>

            <!-- 功能2测试：调试时间 -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-yellow-400">功能2：调试时间测试</h2>
                <div class="space-y-4">
                    <p class="text-gray-300">点击头部的时间按钮可以切换调试模式</p>
                    <div class="flex items-center space-x-4">
                        <button onclick="toggleDebugMode()" class="bg-yellow-600 hover:bg-yellow-700 px-4 py-2 rounded">
                            切换调试模式
                        </button>
                        <span>当前状态：<span id="debugStatus" class="font-semibold">北京时间</span></span>
                    </div>
                    <div class="text-sm text-gray-400">
                        <p>🌈 调试模式下时间显示为彩虹色渐变</p>
                    </div>
                </div>
            </div>

            <!-- 功能3测试：配置加载 -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-orange-400">功能3：增强配置加载测试</h2>
                <div class="space-y-4">
                    <p class="text-gray-300">配置加载现在支持两种模式：</p>
                    <div class="flex space-x-4">
                        <button class="bg-orange-600 hover:bg-orange-700 px-4 py-2 rounded">
                            本地文件
                        </button>
                        <button onclick="showUrlInput()" class="bg-orange-600 hover:bg-orange-700 px-4 py-2 rounded">
                            线上配置
                        </button>
                    </div>
                    <div id="urlInput" class="hidden">
                        <input type="url" placeholder="https://example.com/config.ksconf" 
                               class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                    </div>
                </div>
            </div>

            <!-- 功能4测试：自动滚动 -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-green-400">功能4：自动滚动测试</h2>
                <div class="space-y-4">
                    <p class="text-gray-300">添加产品后会自动滚动到配置列表区域</p>
                    <button onclick="testAutoScroll()" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded">
                        测试自动滚动
                    </button>
                </div>
            </div>

            <!-- 模拟配置列表区域 -->
            <div id="configListSection" class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-purple-400">配置列表区域（滚动目标）</h2>
                <div class="space-y-3">
                    <div class="bg-gray-700 p-4 rounded">红包配置 1</div>
                    <div class="bg-gray-700 p-4 rounded">幸运助手配置 1</div>
                    <div class="bg-gray-700 p-4 rounded">红包配置 2</div>
                </div>
            </div>

            <!-- 更多内容用于测试滚动 -->
            <div class="space-y-4">
                <div class="h-32 bg-gray-700 rounded flex items-center justify-center">更多内容 1</div>
                <div class="h-32 bg-gray-700 rounded flex items-center justify-center">更多内容 2</div>
                <div class="h-32 bg-gray-700 rounded flex items-center justify-center">更多内容 3</div>
            </div>
        </div>
    </main>

    <script>
        let debugMode = false;

        function toggleDebugMode() {
            debugMode = !debugMode;
            const timeLabel = document.getElementById('timeLabel');
            const currentTime = document.getElementById('currentTime');
            const debugStatus = document.getElementById('debugStatus');

            if (debugMode) {
                timeLabel.textContent = '调试时间：';
                currentTime.className = 'debug-time-rainbow font-bold';
                debugStatus.textContent = '调试模式';
                debugStatus.className = 'font-semibold text-yellow-400';
            } else {
                timeLabel.textContent = '北京时间：';
                currentTime.className = '';
                debugStatus.textContent = '北京时间';
                debugStatus.className = 'font-semibold text-green-400';
            }
        }

        function showUrlInput() {
            const urlInput = document.getElementById('urlInput');
            urlInput.classList.toggle('hidden');
        }

        function testAutoScroll() {
            const configListSection = document.getElementById('configListSection');
            const header = document.querySelector('header');
            const headerHeight = header ? header.offsetHeight : 200; // 动态获取头部高度
            const targetPosition = configListSection.offsetTop - headerHeight - 20;

            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }

        // 页面加载后调整主内容区域的padding
        function adjustMainContentPadding() {
            const header = document.querySelector('header');
            const mainContent = document.querySelector('main');

            if (header && mainContent) {
                const headerHeight = header.offsetHeight;
                mainContent.style.paddingTop = `${headerHeight + 20}px`;
            }
        }

        // 页面加载完成后调整padding
        document.addEventListener('DOMContentLoaded', adjustMainContentPadding);
        window.addEventListener('resize', adjustMainContentPadding);

        // 时间按钮点击事件
        document.getElementById('timeBtn').addEventListener('click', toggleDebugMode);
    </script>
</body>
</html>
